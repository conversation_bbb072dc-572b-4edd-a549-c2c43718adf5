-- Database Schema untuk Aplikasi SaveMoney
-- Schema untuk fitur tabungan yang ramah orang tua

-- Tabel untuk menyimpan target tabungan pengguna
CREATE TABLE IF NOT EXISTS savings_targets (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    target_name VARCHAR(255) NOT NULL,
    target_amount DECIMAL(15,2) NOT NULL CHECK (target_amount > 0),
    current_amount DECIMAL(15,2) DEFAULT 0 CHECK (current_amount >= 0),
    target_date DATE,
    agent_name VARCHAR(255),
    agent_phone VARCHAR(20),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT TRUE
);

-- <PERSON>bel untuk menyimpan riwayat setoran
CREATE TABLE IF NOT EXISTS savings_deposits (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    savings_target_id UUID REFERENCES savings_targets(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    amount DECIMAL(15,2) NOT NULL CHECK (amount > 0),
    deposit_date DATE NOT NULL DEFAULT CURRENT_DATE,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabel untuk menyimpan simulasi dan perhitungan target
CREATE TABLE IF NOT EXISTS savings_simulations (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    savings_target_id UUID REFERENCES savings_targets(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    monthly_deposit DECIMAL(15,2) NOT NULL CHECK (monthly_deposit > 0),
    estimated_months INTEGER,
    estimated_completion_date DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Index untuk performa query
CREATE INDEX IF NOT EXISTS idx_savings_targets_user_id ON savings_targets(user_id);
CREATE INDEX IF NOT EXISTS idx_savings_targets_active ON savings_targets(user_id, is_active);
CREATE INDEX IF NOT EXISTS idx_savings_deposits_target_id ON savings_deposits(savings_target_id);
CREATE INDEX IF NOT EXISTS idx_savings_deposits_user_date ON savings_deposits(user_id, deposit_date DESC);
CREATE INDEX IF NOT EXISTS idx_savings_simulations_target_id ON savings_simulations(savings_target_id);

-- Trigger untuk update current_amount di savings_targets ketika ada deposit baru
CREATE OR REPLACE FUNCTION update_savings_target_amount()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE savings_targets
        SET current_amount = current_amount + NEW.amount,
            updated_at = NOW()
        WHERE id = NEW.savings_target_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE savings_targets
        SET current_amount = current_amount - OLD.amount,
            updated_at = NOW()
        WHERE id = OLD.savings_target_id;
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        UPDATE savings_targets
        SET current_amount = current_amount - OLD.amount + NEW.amount,
            updated_at = NOW()
        WHERE id = NEW.savings_target_id;
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Trigger untuk otomatis update current_amount
DROP TRIGGER IF EXISTS trigger_update_savings_amount ON savings_deposits;
CREATE TRIGGER trigger_update_savings_amount
    AFTER INSERT OR UPDATE OR DELETE ON savings_deposits
    FOR EACH ROW EXECUTE FUNCTION update_savings_target_amount();

-- Row Level Security (RLS) untuk keamanan data
ALTER TABLE savings_targets ENABLE ROW LEVEL SECURITY;
ALTER TABLE savings_deposits ENABLE ROW LEVEL SECURITY;
ALTER TABLE savings_simulations ENABLE ROW LEVEL SECURITY;

-- Policy untuk savings_targets
CREATE POLICY "Users can view their own savings targets" ON savings_targets
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own savings targets" ON savings_targets
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own savings targets" ON savings_targets
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own savings targets" ON savings_targets
    FOR DELETE USING (auth.uid() = user_id);

-- Policy untuk savings_deposits
CREATE POLICY "Users can view their own deposits" ON savings_deposits
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own deposits" ON savings_deposits
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own deposits" ON savings_deposits
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own deposits" ON savings_deposits
    FOR DELETE USING (auth.uid() = user_id);

-- Policy untuk savings_simulations
CREATE POLICY "Users can view their own simulations" ON savings_simulations
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own simulations" ON savings_simulations
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own simulations" ON savings_simulations
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own simulations" ON savings_simulations
    FOR DELETE USING (auth.uid() = user_id);