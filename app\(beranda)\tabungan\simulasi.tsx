import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import React, { useState, useEffect } from 'react';
import {
  SafeAreaView,
  ScrollView,
  Text,
  TextInput,
  TouchableOpacity,
  View,
  Alert,
} from 'react-native';
import { useAuth } from '../../../contexts/AuthContext';
import { supabase } from '../../../utils/supabase';
import { tabunganStyles } from './styles';

interface SavingsTarget {
  id: string;
  target_name: string;
  target_amount: number;
  current_amount: number;
  target_date: string | null;
}

interface SimulationResult {
  monthlyAmount: number;
  remainingAmount: number;
  monthsNeeded: number;
  estimatedDate: string;
  canReachTarget: boolean;
  targetDatePassed: boolean;
}

export default function SimulasiScreen() {
  const { user } = useAuth();
  const [savingsTarget, setSavingsTarget] = useState<SavingsTarget | null>(null);
  const [monthlyAmount, setMonthlyAmount] = useState('');
  const [simulation, setSimulation] = useState<SimulationResult | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadSavingsTarget();
  }, [user]);

  useEffect(() => {
    if (monthlyAmount && savingsTarget) {
      calculateSimulation();
    } else {
      setSimulation(null);
    }
  }, [monthlyAmount, savingsTarget]);

  const loadSavingsTarget = async () => {
    if (!user) return;

    try {
      const { data, error } = await supabase
        .from('savings_targets')
        .select('*')
        .eq('user_id', user.id)
        .eq('is_active', true)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          Alert.alert(
            'Belum Ada Target',
            'Anda belum memiliki target tabungan.',
            [
              { text: 'Buat Target', onPress: () => router.replace('/tabungan/buat-target') },
              { text: 'Kembali', onPress: () => router.back() }
            ]
          );
        } else {
          console.error('Error loading savings target:', error);
          Alert.alert('Error', 'Gagal memuat data target tabungan');
        }
      } else {
        setSavingsTarget(data);
      }
    } catch (error) {
      console.error('Error loading savings target:', error);
      Alert.alert('Error', 'Gagal memuat data target tabungan');
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatInputCurrency = (value: string) => {
    const numericValue = value.replace(/[^0-9]/g, '');
    if (numericValue === '') return '';
    const number = parseInt(numericValue);
    return new Intl.NumberFormat('id-ID').format(number);
  };

  const parseAmount = (formattedAmount: string): number => {
    return parseInt(formattedAmount.replace(/[^0-9]/g, '')) || 0;
  };

  const handleAmountChange = (value: string) => {
    const formatted = formatInputCurrency(value);
    setMonthlyAmount(formatted);
  };

  const calculateSimulation = () => {
    if (!savingsTarget) return;

    const monthly = parseAmount(monthlyAmount);
    if (monthly <= 0) {
      setSimulation(null);
      return;
    }

    const remainingAmount = Math.max(savingsTarget.target_amount - savingsTarget.current_amount, 0);
    
    if (remainingAmount === 0) {
      setSimulation({
        monthlyAmount: monthly,
        remainingAmount: 0,
        monthsNeeded: 0,
        estimatedDate: 'Target sudah tercapai!',
        canReachTarget: true,
        targetDatePassed: false,
      });
      return;
    }

    const monthsNeeded = Math.ceil(remainingAmount / monthly);
    const estimatedDate = new Date();
    estimatedDate.setMonth(estimatedDate.getMonth() + monthsNeeded);

    let targetDatePassed = false;
    let canReachTarget = true;

    if (savingsTarget.target_date) {
      const targetDate = new Date(savingsTarget.target_date);
      targetDatePassed = estimatedDate > targetDate;
      
      if (targetDatePassed) {
        const monthsUntilTarget = Math.ceil(
          (targetDate.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24 * 30)
        );
        canReachTarget = monthsUntilTarget > 0 && (remainingAmount / monthsUntilTarget) <= monthly * 2;
      }
    }

    setSimulation({
      monthlyAmount: monthly,
      remainingAmount,
      monthsNeeded,
      estimatedDate: estimatedDate.toLocaleDateString('id-ID', {
        month: 'long',
        year: 'numeric'
      }),
      canReachTarget,
      targetDatePassed,
    });
  };

  const getRecommendedAmount = () => {
    if (!savingsTarget || !savingsTarget.target_date) return null;

    const remainingAmount = Math.max(savingsTarget.target_amount - savingsTarget.current_amount, 0);
    const targetDate = new Date(savingsTarget.target_date);
    const now = new Date();
    const monthsUntilTarget = Math.max(
      Math.ceil((targetDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24 * 30)),
      1
    );

    return Math.ceil(remainingAmount / monthsUntilTarget);
  };

  const quickAmounts = [100000, 200000, 500000, 1000000];

  const handleQuickAmount = (amount: number) => {
    setMonthlyAmount(new Intl.NumberFormat('id-ID').format(amount));
  };

  if (loading) {
    return (
      <SafeAreaView style={tabunganStyles.container}>
        <View style={tabunganStyles.loadingContainer}>
          <Text style={tabunganStyles.loadingText}>Memuat data...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (!savingsTarget) {
    return (
      <SafeAreaView style={tabunganStyles.container}>
        <View style={tabunganStyles.emptyContainer}>
          <Ionicons name="calculator-outline" size={64} color="#ccc" />
          <Text style={tabunganStyles.emptyTitle}>Belum Ada Target Tabungan</Text>
          <Text style={tabunganStyles.emptyDescription}>
            Silakan buat target tabungan terlebih dahulu
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  const recommendedAmount = getRecommendedAmount();
  const progressPercentage = Math.min(
    Math.round((savingsTarget.current_amount / savingsTarget.target_amount) * 100),
    100
  );

  return (
    <SafeAreaView style={tabunganStyles.container}>
      <ScrollView contentContainerStyle={tabunganStyles.scrollContent}>
        {/* Header */}
        <View style={tabunganStyles.header}>
          <TouchableOpacity
            style={{ position: 'absolute', left: 0, top: 0 }}
            onPress={() => router.back()}
          >
            <Ionicons name="arrow-back" size={28} color="#2E7D32" />
          </TouchableOpacity>
          <Text style={tabunganStyles.pageTitle}>🧮 Simulasi Target</Text>
          <Text style={tabunganStyles.subtitle}>
            Hitung berapa lagi yang perlu ditabung
          </Text>
        </View>

        {/* Current Status */}
        <View style={tabunganStyles.progressSection}>
          <Text style={tabunganStyles.sectionTitle}>Status Saat Ini</Text>
          <Text style={tabunganStyles.targetName}>{savingsTarget.target_name}</Text>
          
          <View style={tabunganStyles.amountContainer}>
            <Text style={tabunganStyles.currentAmount}>
              {formatCurrency(savingsTarget.current_amount)}
            </Text>
            <Text style={tabunganStyles.targetAmount}>
              dari {formatCurrency(savingsTarget.target_amount)}
            </Text>
          </View>

          <View style={tabunganStyles.progressContainer}>
            <View style={tabunganStyles.progressBar}>
              <View
                style={[
                  tabunganStyles.progressFill,
                  { width: `${progressPercentage}%` }
                ]}
              />
            </View>
            <Text style={tabunganStyles.progressText}>{progressPercentage}%</Text>
          </View>

          <Text style={tabunganStyles.remainingText}>
            Sisa yang perlu ditabung: {formatCurrency(savingsTarget.target_amount - savingsTarget.current_amount)}
          </Text>

          {savingsTarget.target_date && (
            <Text style={[tabunganStyles.remainingText, { marginTop: 8 }]}>
              Target tanggal: {new Date(savingsTarget.target_date).toLocaleDateString('id-ID', {
                day: 'numeric',
                month: 'long',
                year: 'numeric'
              })}
            </Text>
          )}
        </View>

        {/* Simulation Input */}
        <View style={tabunganStyles.formContainer}>
          <Text style={tabunganStyles.sectionTitle}>Simulasi Menabung</Text>
          
          {recommendedAmount && (
            <View style={{ backgroundColor: '#e8f5e8', padding: 16, borderRadius: 12, marginBottom: 16 }}>
              <Text style={{ fontSize: 16, fontWeight: '600', color: '#2E7D32', marginBottom: 4 }}>
                💡 Rekomendasi
              </Text>
              <Text style={{ fontSize: 14, color: '#2E7D32' }}>
                Untuk mencapai target tepat waktu, Anda perlu menabung minimal{' '}
                <Text style={{ fontWeight: 'bold' }}>{formatCurrency(recommendedAmount)}</Text> per bulan
              </Text>
            </View>
          )}

          <View style={tabunganStyles.inputContainer}>
            <Text style={tabunganStyles.label}>Jumlah Setoran per Bulan</Text>
            <TextInput
              style={[tabunganStyles.input, { fontSize: 20, fontWeight: 'bold' }]}
              value={monthlyAmount}
              onChangeText={handleAmountChange}
              placeholder="0"
              keyboardType="numeric"
              placeholderTextColor="#ccc"
            />
          </View>

          {/* Quick Amount Buttons */}
          <View style={{ marginBottom: 20 }}>
            <Text style={[tabunganStyles.label, { marginBottom: 12 }]}>
              Jumlah Cepat
            </Text>
            <View style={{ flexDirection: 'row', flexWrap: 'wrap', gap: 8 }}>
              {quickAmounts.map((amount) => (
                <TouchableOpacity
                  key={amount}
                  style={[
                    tabunganStyles.secondaryButton,
                    { flex: 0, paddingHorizontal: 16, paddingVertical: 12 }
                  ]}
                  onPress={() => handleQuickAmount(amount)}
                >
                  <Text style={tabunganStyles.secondaryButtonText}>
                    {formatCurrency(amount)}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        </View>

        {/* Simulation Result */}
        {simulation && (
          <View style={tabunganStyles.formContainer}>
            <Text style={tabunganStyles.sectionTitle}>Hasil Simulasi</Text>
            
            <View style={{ gap: 16 }}>
              <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
                <Text style={{ fontSize: 16, color: '#666' }}>Setoran per bulan:</Text>
                <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#333' }}>
                  {formatCurrency(simulation.monthlyAmount)}
                </Text>
              </View>

              <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
                <Text style={{ fontSize: 16, color: '#666' }}>Sisa yang perlu ditabung:</Text>
                <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#FF9800' }}>
                  {formatCurrency(simulation.remainingAmount)}
                </Text>
              </View>

              <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
                <Text style={{ fontSize: 16, color: '#666' }}>Waktu yang dibutuhkan:</Text>
                <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#2196F3' }}>
                  {simulation.monthsNeeded} bulan
                </Text>
              </View>

              <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
                <Text style={{ fontSize: 16, color: '#666' }}>Perkiraan selesai:</Text>
                <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#4CAF50' }}>
                  {simulation.estimatedDate}
                </Text>
              </View>

              {simulation.targetDatePassed && (
                <View style={{ backgroundColor: '#fff3cd', padding: 16, borderRadius: 12, marginTop: 8 }}>
                  <Text style={{ fontSize: 16, fontWeight: '600', color: '#856404', marginBottom: 4 }}>
                    ⚠️ Perhatian
                  </Text>
                  <Text style={{ fontSize: 14, color: '#856404' }}>
                    Dengan setoran ini, target akan tercapai melewati tanggal yang diinginkan.
                    Pertimbangkan untuk menambah jumlah setoran bulanan.
                  </Text>
                </View>
              )}

              {simulation.remainingAmount === 0 && (
                <View style={{ backgroundColor: '#d4edda', padding: 16, borderRadius: 12, marginTop: 8 }}>
                  <Text style={{ fontSize: 16, fontWeight: '600', color: '#155724', marginBottom: 4 }}>
                    🎉 Selamat!
                  </Text>
                  <Text style={{ fontSize: 14, color: '#155724' }}>
                    Target tabungan Anda sudah tercapai!
                  </Text>
                </View>
              )}
            </View>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}
