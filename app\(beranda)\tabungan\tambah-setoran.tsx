import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import React, { useState, useEffect } from 'react';
import {
  SafeAreaView,
  ScrollView,
  Text,
  TextInput,
  TouchableOpacity,
  View,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { useAuth } from '../../../contexts/AuthContext';
import { supabase } from '../../../utils/supabase';
import { tabunganStyles } from './styles';

interface SavingsTarget {
  id: string;
  target_name: string;
  target_amount: number;
  current_amount: number;
}

export default function TambahSetoranScreen() {
  const { user } = useAuth();
  const [savingsTarget, setSavingsTarget] = useState<SavingsTarget | null>(null);
  const [amount, setAmount] = useState('');
  const [notes, setNotes] = useState('');
  const [depositDate, setDepositDate] = useState(new Date().toISOString().split('T')[0]);
  const [loading, setLoading] = useState(false);
  const [loadingTarget, setLoadingTarget] = useState(true);

  useEffect(() => {
    loadSavingsTarget();
  }, [user]);

  const loadSavingsTarget = async () => {
    if (!user) return;

    try {
      const { data, error } = await supabase
        .from('savings_targets')
        .select('*')
        .eq('user_id', user.id)
        .eq('is_active', true)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          Alert.alert(
            'Belum Ada Target',
            'Anda belum memiliki target tabungan. Silakan buat target terlebih dahulu.',
            [
              { text: 'Buat Target', onPress: () => router.replace('/tabungan/buat-target') },
              { text: 'Kembali', onPress: () => router.back() }
            ]
          );
        } else {
          console.error('Error loading savings target:', error);
          Alert.alert('Error', 'Gagal memuat data target tabungan');
        }
      } else {
        setSavingsTarget(data);
      }
    } catch (error) {
      console.error('Error loading savings target:', error);
      Alert.alert('Error', 'Gagal memuat data target tabungan');
    } finally {
      setLoadingTarget(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatInputCurrency = (value: string) => {
    // Remove non-numeric characters
    const numericValue = value.replace(/[^0-9]/g, '');
    
    if (numericValue === '') return '';
    
    // Format as currency
    const number = parseInt(numericValue);
    return new Intl.NumberFormat('id-ID').format(number);
  };

  const parseAmount = (formattedAmount: string): number => {
    return parseInt(formattedAmount.replace(/[^0-9]/g, '')) || 0;
  };

  const handleAmountChange = (value: string) => {
    const formatted = formatInputCurrency(value);
    setAmount(formatted);
  };

  const handleSubmit = async () => {
    if (!savingsTarget || !user) return;

    const numericAmount = parseAmount(amount);

    if (numericAmount <= 0) {
      Alert.alert('Error', 'Jumlah setoran harus lebih dari 0');
      return;
    }

    if (!depositDate) {
      Alert.alert('Error', 'Tanggal setoran harus diisi');
      return;
    }

    setLoading(true);

    try {
      const { error } = await supabase
        .from('savings_deposits')
        .insert({
          savings_target_id: savingsTarget.id,
          user_id: user.id,
          amount: numericAmount,
          deposit_date: depositDate,
          notes: notes.trim() || null,
        });

      if (error) {
        console.error('Error adding deposit:', error);
        Alert.alert('Error', 'Gagal menambahkan setoran');
      } else {
        Alert.alert(
          'Berhasil!',
          `Setoran sebesar ${formatCurrency(numericAmount)} berhasil ditambahkan`,
          [
            {
              text: 'OK',
              onPress: () => router.back(),
            },
          ]
        );
      }
    } catch (error) {
      console.error('Error adding deposit:', error);
      Alert.alert('Error', 'Gagal menambahkan setoran');
    } finally {
      setLoading(false);
    }
  };

  const quickAmounts = [50000, 100000, 200000, 500000];

  const handleQuickAmount = (quickAmount: number) => {
    setAmount(new Intl.NumberFormat('id-ID').format(quickAmount));
  };

  if (loadingTarget) {
    return (
      <SafeAreaView style={tabunganStyles.container}>
        <View style={tabunganStyles.loadingContainer}>
          <Text style={tabunganStyles.loadingText}>Memuat data...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (!savingsTarget) {
    return (
      <SafeAreaView style={tabunganStyles.container}>
        <View style={tabunganStyles.emptyContainer}>
          <Ionicons name="wallet-outline" size={64} color="#ccc" />
          <Text style={tabunganStyles.emptyTitle}>Belum Ada Target Tabungan</Text>
          <Text style={tabunganStyles.emptyDescription}>
            Silakan buat target tabungan terlebih dahulu
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={tabunganStyles.container}>
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView contentContainerStyle={tabunganStyles.scrollContent}>
          {/* Header */}
          <View style={tabunganStyles.header}>
            <TouchableOpacity
              style={{ position: 'absolute', left: 0, top: 0 }}
              onPress={() => router.back()}
            >
              <Ionicons name="arrow-back" size={28} color="#2E7D32" />
            </TouchableOpacity>
            <Text style={tabunganStyles.pageTitle}>Tambah Setoran</Text>
            <Text style={tabunganStyles.subtitle}>
              Target: {savingsTarget.target_name}
            </Text>
          </View>

          {/* Current Progress */}
          <View style={tabunganStyles.progressSection}>
            <Text style={tabunganStyles.sectionTitle}>Saldo Saat Ini</Text>
            <Text style={tabunganStyles.currentAmount}>
              {formatCurrency(savingsTarget.current_amount)}
            </Text>
            <Text style={tabunganStyles.targetAmount}>
              dari {formatCurrency(savingsTarget.target_amount)}
            </Text>
          </View>

          {/* Form */}
          <View style={tabunganStyles.formContainer}>
            <View style={tabunganStyles.inputContainer}>
              <Text style={tabunganStyles.label}>Jumlah Setoran *</Text>
              <TextInput
                style={[tabunganStyles.input, { fontSize: 24, fontWeight: 'bold' }]}
                value={amount}
                onChangeText={handleAmountChange}
                placeholder="0"
                keyboardType="numeric"
                placeholderTextColor="#ccc"
              />
            </View>

            {/* Quick Amount Buttons */}
            <View style={{ marginBottom: 20 }}>
              <Text style={[tabunganStyles.label, { marginBottom: 12 }]}>
                Jumlah Cepat
              </Text>
              <View style={{ flexDirection: 'row', flexWrap: 'wrap', gap: 8 }}>
                {quickAmounts.map((quickAmount) => (
                  <TouchableOpacity
                    key={quickAmount}
                    style={[
                      tabunganStyles.secondaryButton,
                      { flex: 0, paddingHorizontal: 16, paddingVertical: 12 }
                    ]}
                    onPress={() => handleQuickAmount(quickAmount)}
                  >
                    <Text style={tabunganStyles.secondaryButtonText}>
                      {formatCurrency(quickAmount)}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            <View style={tabunganStyles.inputContainer}>
              <Text style={tabunganStyles.label}>Tanggal Setoran *</Text>
              <TextInput
                style={tabunganStyles.input}
                value={depositDate}
                onChangeText={setDepositDate}
                placeholder="YYYY-MM-DD"
                placeholderTextColor="#ccc"
              />
              <Text style={{ fontSize: 14, color: '#666', marginTop: 4 }}>
                Format: YYYY-MM-DD (contoh: 2024-01-15)
              </Text>
            </View>

            <View style={tabunganStyles.inputContainer}>
              <Text style={tabunganStyles.label}>Catatan (Opsional)</Text>
              <TextInput
                style={[tabunganStyles.input, { height: 80, textAlignVertical: 'top' }]}
                value={notes}
                onChangeText={setNotes}
                placeholder="Tambahkan catatan untuk setoran ini..."
                multiline
                placeholderTextColor="#ccc"
              />
            </View>

            <TouchableOpacity
              style={[
                tabunganStyles.button,
                loading && tabunganStyles.buttonDisabled
              ]}
              onPress={handleSubmit}
              disabled={loading}
            >
              <Text style={tabunganStyles.buttonText}>
                {loading ? 'Menyimpan...' : 'Tambah Setoran'}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={tabunganStyles.secondaryButton}
              onPress={() => router.back()}
            >
              <Text style={tabunganStyles.secondaryButtonText}>Batal</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}
