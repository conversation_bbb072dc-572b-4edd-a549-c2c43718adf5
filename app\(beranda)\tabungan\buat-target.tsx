import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import React, { useState } from 'react';
import {
  SafeAreaView,
  ScrollView,
  Text,
  TextInput,
  TouchableOpacity,
  View,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { useAuth } from '../../../contexts/AuthContext';
import { supabase } from '../../../utils/supabase';
import { tabunganStyles } from './styles';

export default function BuatTargetScreen() {
  const { user } = useAuth();
  const [targetName, setTargetName] = useState('');
  const [targetAmount, setTargetAmount] = useState('');
  const [targetDate, setTargetDate] = useState('');
  const [agentName, setAgentName] = useState('');
  const [agentPhone, setAgentPhone] = useState('');
  const [loading, setLoading] = useState(false);

  const formatCurrency = (value: string) => {
    const numericValue = value.replace(/[^0-9]/g, '');
    if (numericValue === '') return '';
    const number = parseInt(numericValue);
    return new Intl.NumberFormat('id-ID').format(number);
  };

  const parseAmount = (formattedAmount: string): number => {
    return parseInt(formattedAmount.replace(/[^0-9]/g, '')) || 0;
  };

  const handleAmountChange = (value: string) => {
    const formatted = formatCurrency(value);
    setTargetAmount(formatted);
  };

  const validateForm = () => {
    if (!targetName.trim()) {
      Alert.alert('Error', 'Nama target tabungan harus diisi');
      return false;
    }

    const amount = parseAmount(targetAmount);
    if (amount <= 0) {
      Alert.alert('Error', 'Jumlah target harus lebih dari 0');
      return false;
    }

    if (targetDate && !isValidDate(targetDate)) {
      Alert.alert('Error', 'Format tanggal tidak valid. Gunakan format YYYY-MM-DD');
      return false;
    }

    if (targetDate) {
      const date = new Date(targetDate);
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      
      if (date <= today) {
        Alert.alert('Error', 'Tanggal target harus di masa depan');
        return false;
      }
    }

    return true;
  };

  const isValidDate = (dateString: string) => {
    const regex = /^\d{4}-\d{2}-\d{2}$/;
    if (!regex.test(dateString)) return false;
    
    const date = new Date(dateString);
    return date instanceof Date && !isNaN(date.getTime());
  };

  const handleSubmit = async () => {
    if (!user || !validateForm()) return;

    setLoading(true);

    try {
      // Check if user already has an active target
      const { data: existingTarget, error: checkError } = await supabase
        .from('savings_targets')
        .select('id')
        .eq('user_id', user.id)
        .eq('is_active', true)
        .single();

      if (checkError && checkError.code !== 'PGRST116') {
        console.error('Error checking existing target:', checkError);
        Alert.alert('Error', 'Gagal memeriksa target yang ada');
        return;
      }

      if (existingTarget) {
        Alert.alert(
          'Target Sudah Ada',
          'Anda sudah memiliki target tabungan aktif. Hanya bisa memiliki satu target aktif dalam satu waktu.',
          [{ text: 'OK' }]
        );
        return;
      }

      const { error } = await supabase
        .from('savings_targets')
        .insert({
          user_id: user.id,
          target_name: targetName.trim(),
          target_amount: parseAmount(targetAmount),
          target_date: targetDate || null,
          agent_name: agentName.trim() || null,
          agent_phone: agentPhone.trim() || null,
        });

      if (error) {
        console.error('Error creating target:', error);
        Alert.alert('Error', 'Gagal membuat target tabungan');
      } else {
        Alert.alert(
          'Berhasil!',
          'Target tabungan berhasil dibuat',
          [
            {
              text: 'OK',
              onPress: () => router.replace('/(beranda)/tabungan'),
            },
          ]
        );
      }
    } catch (error) {
      console.error('Error creating target:', error);
      Alert.alert('Error', 'Gagal membuat target tabungan');
    } finally {
      setLoading(false);
    }
  };

  const quickTargets = [
    { name: 'Liburan Keluarga', amount: 5000000 },
    { name: 'Dana Darurat', amount: 10000000 },
    { name: 'Beli Motor', amount: 15000000 },
    { name: 'Renovasi Rumah', amount: 25000000 },
  ];

  const handleQuickTarget = (target: { name: string; amount: number }) => {
    setTargetName(target.name);
    setTargetAmount(new Intl.NumberFormat('id-ID').format(target.amount));
  };

  return (
    <SafeAreaView style={tabunganStyles.container}>
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView contentContainerStyle={tabunganStyles.scrollContent}>
          {/* Header */}
          <View style={tabunganStyles.header}>
            <TouchableOpacity
              style={{ position: 'absolute', left: 0, top: 0 }}
              onPress={() => router.back()}
            >
              <Ionicons name="arrow-back" size={28} color="#2E7D32" />
            </TouchableOpacity>
            <Text style={tabunganStyles.pageTitle}>🎯 Buat Target Baru</Text>
            <Text style={tabunganStyles.subtitle}>
              Tentukan tujuan tabungan Anda
            </Text>
          </View>

          {/* Quick Targets */}
          <View style={tabunganStyles.formContainer}>
            <Text style={tabunganStyles.sectionTitle}>Target Populer</Text>
            <Text style={{ fontSize: 14, color: '#666', marginBottom: 16 }}>
              Pilih salah satu atau buat target sendiri
            </Text>
            <View style={{ gap: 8 }}>
              {quickTargets.map((target, index) => (
                <TouchableOpacity
                  key={index}
                  style={[
                    tabunganStyles.secondaryButton,
                    { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }
                  ]}
                  onPress={() => handleQuickTarget(target)}
                >
                  <Text style={tabunganStyles.secondaryButtonText}>
                    {target.name}
                  </Text>
                  <Text style={[tabunganStyles.secondaryButtonText, { fontWeight: 'bold' }]}>
                    {new Intl.NumberFormat('id-ID', {
                      style: 'currency',
                      currency: 'IDR',
                      minimumFractionDigits: 0,
                    }).format(target.amount)}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* Form */}
          <View style={tabunganStyles.formContainer}>
            <Text style={tabunganStyles.sectionTitle}>Detail Target</Text>

            <View style={tabunganStyles.inputContainer}>
              <Text style={tabunganStyles.label}>Nama Target Tabungan *</Text>
              <TextInput
                style={tabunganStyles.input}
                value={targetName}
                onChangeText={setTargetName}
                placeholder="Contoh: Liburan ke Bali"
                placeholderTextColor="#ccc"
              />
            </View>

            <View style={tabunganStyles.inputContainer}>
              <Text style={tabunganStyles.label}>Jumlah Target *</Text>
              <TextInput
                style={[tabunganStyles.input, { fontSize: 20, fontWeight: 'bold' }]}
                value={targetAmount}
                onChangeText={handleAmountChange}
                placeholder="0"
                keyboardType="numeric"
                placeholderTextColor="#ccc"
              />
            </View>

            <View style={tabunganStyles.inputContainer}>
              <Text style={tabunganStyles.label}>Tanggal Target (Opsional)</Text>
              <TextInput
                style={tabunganStyles.input}
                value={targetDate}
                onChangeText={setTargetDate}
                placeholder="YYYY-MM-DD"
                placeholderTextColor="#ccc"
              />
              <Text style={{ fontSize: 14, color: '#666', marginTop: 4 }}>
                Format: YYYY-MM-DD (contoh: 2024-12-31)
              </Text>
            </View>

            <View style={tabunganStyles.inputContainer}>
              <Text style={tabunganStyles.label}>Nama Agent (Opsional)</Text>
              <TextInput
                style={tabunganStyles.input}
                value={agentName}
                onChangeText={setAgentName}
                placeholder="Contoh: Ibu Sari"
                placeholderTextColor="#ccc"
              />
            </View>

            <View style={tabunganStyles.inputContainer}>
              <Text style={tabunganStyles.label}>Nomor Telepon Agent (Opsional)</Text>
              <TextInput
                style={tabunganStyles.input}
                value={agentPhone}
                onChangeText={setAgentPhone}
                placeholder="Contoh: 0812-3456-7890"
                keyboardType="phone-pad"
                placeholderTextColor="#ccc"
              />
            </View>

            <TouchableOpacity
              style={[
                tabunganStyles.button,
                loading && tabunganStyles.buttonDisabled
              ]}
              onPress={handleSubmit}
              disabled={loading}
            >
              <Text style={tabunganStyles.buttonText}>
                {loading ? 'Membuat Target...' : 'Buat Target Tabungan'}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={tabunganStyles.secondaryButton}
              onPress={() => router.back()}
            >
              <Text style={tabunganStyles.secondaryButtonText}>Batal</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}
