import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import React, { useState, useEffect } from 'react';
import {
  SafeAreaView,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
  Alert,
  RefreshControl,
} from 'react-native';
import { useAuth } from '../../../contexts/AuthContext';
import { supabase } from '../../../utils/supabase';
import { tabunganStyles } from './styles';

interface Deposit {
  id: string;
  amount: number;
  deposit_date: string;
  notes: string | null;
  created_at: string;
}

interface SavingsTarget {
  id: string;
  target_name: string;
  target_amount: number;
  current_amount: number;
}

export default function RiwayatScreen() {
  const { user } = useAuth();
  const [deposits, setDeposits] = useState<Deposit[]>([]);
  const [savingsTarget, setSavingsTarget] = useState<SavingsTarget | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadData();
  }, [user]);

  const loadData = async () => {
    if (!user) return;

    try {
      // Load active savings target
      const { data: targetData, error: targetError } = await supabase
        .from('savings_targets')
        .select('*')
        .eq('user_id', user.id)
        .eq('is_active', true)
        .single();

      if (targetError) {
        if (targetError.code === 'PGRST116') {
          Alert.alert(
            'Belum Ada Target',
            'Anda belum memiliki target tabungan.',
            [
              { text: 'Buat Target', onPress: () => router.replace('/tabungan/buat-target') },
              { text: 'Kembali', onPress: () => router.back() }
            ]
          );
        } else {
          console.error('Error loading savings target:', targetError);
        }
        return;
      }

      setSavingsTarget(targetData);

      // Load deposits for this target
      const { data: depositsData, error: depositsError } = await supabase
        .from('savings_deposits')
        .select('*')
        .eq('savings_target_id', targetData.id)
        .order('deposit_date', { ascending: false });

      if (depositsError) {
        console.error('Error loading deposits:', depositsError);
        Alert.alert('Error', 'Gagal memuat riwayat setoran');
      } else {
        setDeposits(depositsData || []);
      }
    } catch (error) {
      console.error('Error loading data:', error);
      Alert.alert('Error', 'Gagal memuat data');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    loadData();
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('id-ID', {
      weekday: 'long',
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    });
  };

  const formatShortDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('id-ID', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  const getTotalDeposits = () => {
    return deposits.reduce((total, deposit) => total + deposit.amount, 0);
  };

  const groupDepositsByMonth = () => {
    const grouped: { [key: string]: Deposit[] } = {};
    
    deposits.forEach(deposit => {
      const date = new Date(deposit.deposit_date);
      const monthKey = date.toLocaleDateString('id-ID', {
        month: 'long',
        year: 'numeric'
      });
      
      if (!grouped[monthKey]) {
        grouped[monthKey] = [];
      }
      grouped[monthKey].push(deposit);
    });

    return grouped;
  };

  if (loading) {
    return (
      <SafeAreaView style={tabunganStyles.container}>
        <View style={tabunganStyles.loadingContainer}>
          <Text style={tabunganStyles.loadingText}>Memuat riwayat...</Text>
        </View>
      </SafeAreaView>
    );
  }

  const groupedDeposits = groupDepositsByMonth();

  return (
    <SafeAreaView style={tabunganStyles.container}>
      <ScrollView 
        contentContainerStyle={tabunganStyles.scrollContent}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Header */}
        <View style={tabunganStyles.header}>
          <TouchableOpacity
            style={{ position: 'absolute', left: 0, top: 0 }}
            onPress={() => router.back()}
          >
            <Ionicons name="arrow-back" size={28} color="#2E7D32" />
          </TouchableOpacity>
          <Text style={tabunganStyles.pageTitle}>📋 Riwayat Setoran</Text>
          {savingsTarget && (
            <Text style={tabunganStyles.subtitle}>
              Target: {savingsTarget.target_name}
            </Text>
          )}
        </View>

        {/* Summary */}
        {deposits.length > 0 && (
          <View style={tabunganStyles.progressSection}>
            <Text style={tabunganStyles.sectionTitle}>Ringkasan</Text>
            <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginBottom: 12 }}>
              <View style={{ flex: 1, alignItems: 'center' }}>
                <Text style={tabunganStyles.targetAmount}>Total Setoran</Text>
                <Text style={tabunganStyles.currentAmount}>
                  {formatCurrency(getTotalDeposits())}
                </Text>
              </View>
              <View style={{ flex: 1, alignItems: 'center' }}>
                <Text style={tabunganStyles.targetAmount}>Jumlah Transaksi</Text>
                <Text style={[tabunganStyles.currentAmount, { color: '#2196F3' }]}>
                  {deposits.length}
                </Text>
              </View>
            </View>
          </View>
        )}

        {/* Deposits List */}
        {deposits.length > 0 ? (
          Object.entries(groupedDeposits).map(([month, monthDeposits]) => (
            <View key={month} style={tabunganStyles.listContainer}>
              <View style={{ padding: 16, backgroundColor: '#f8f9fa', borderTopLeftRadius: 16, borderTopRightRadius: 16 }}>
                <Text style={[tabunganStyles.sectionTitle, { marginBottom: 0, fontSize: 18 }]}>
                  {month}
                </Text>
                <Text style={{ fontSize: 14, color: '#666', marginTop: 4 }}>
                  {monthDeposits.length} setoran • {formatCurrency(
                    monthDeposits.reduce((sum, d) => sum + d.amount, 0)
                  )}
                </Text>
              </View>
              
              {monthDeposits.map((deposit, index) => (
                <View
                  key={deposit.id}
                  style={[
                    tabunganStyles.listItem,
                    index === monthDeposits.length - 1 && tabunganStyles.listItemLast
                  ]}
                >
                  <View style={tabunganStyles.listIcon}>
                    <Ionicons name="arrow-up-circle" size={32} color="#4CAF50" />
                  </View>
                  <View style={tabunganStyles.listContent}>
                    <Text style={tabunganStyles.listAmount}>
                      {formatCurrency(deposit.amount)}
                    </Text>
                    <Text style={tabunganStyles.listTitle}>
                      {formatDate(deposit.deposit_date)}
                    </Text>
                    {deposit.notes && (
                      <Text style={tabunganStyles.listSubtitle}>
                        {deposit.notes}
                      </Text>
                    )}
                  </View>
                  <View style={{ alignItems: 'flex-end' }}>
                    <Text style={{ fontSize: 12, color: '#999' }}>
                      {formatShortDate(deposit.deposit_date)}
                    </Text>
                  </View>
                </View>
              ))}
            </View>
          ))
        ) : (
          <View style={tabunganStyles.emptyContainer}>
            <Ionicons name="receipt-outline" size={64} color="#ccc" style={tabunganStyles.emptyIcon} />
            <Text style={tabunganStyles.emptyTitle}>Belum Ada Setoran</Text>
            <Text style={tabunganStyles.emptyDescription}>
              Mulai menabung dengan menambahkan setoran pertama Anda
            </Text>
            <TouchableOpacity
              style={[tabunganStyles.button, { marginTop: 20 }]}
              onPress={() => router.push('/tabungan/tambah-setoran')}
            >
              <Text style={tabunganStyles.buttonText}>Tambah Setoran</Text>
            </TouchableOpacity>
          </View>
        )}

        {/* Add Deposit Button */}
        {deposits.length > 0 && (
          <TouchableOpacity
            style={tabunganStyles.button}
            onPress={() => router.push('/tabungan/tambah-setoran')}
          >
            <Ionicons name="add" size={24} color="#fff" style={{ marginRight: 8 }} />
            <Text style={tabunganStyles.buttonText}>Tambah Setoran Baru</Text>
          </TouchableOpacity>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}
