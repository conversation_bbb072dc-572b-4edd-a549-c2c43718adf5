import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import React, { useEffect, useState } from 'react';
import { 
  SafeAreaView, 
  ScrollView, 
  Text, 
  TouchableOpacity, 
  View, 
  Alert,
  RefreshControl 
} from 'react-native';
import { useAuth } from '../../contexts/AuthContext';
import { supabase } from '../../utils/supabase';
import { tabunganStyles } from './tabungan/styles';

interface SavingsTarget {
  id: string;
  target_name: string;
  target_amount: number;
  current_amount: number;
  target_date: string | null;
  agent_name: string | null;
  agent_phone: string | null;
}

interface RecentDeposit {
  id: string;
  amount: number;
  deposit_date: string;
  notes: string | null;
}

export default function TabunganScreen() {
  const { user } = useAuth();
  const [savingsTarget, setSavingsTarget] = useState<SavingsTarget | null>(null);
  const [recentDeposits, setRecentDeposits] = useState<RecentDeposit[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  const loadSavingsData = async () => {
    if (!user) return;

    try {
      // Load active savings target
      const { data: targetData, error: targetError } = await supabase
        .from('savings_targets')
        .select('*')
        .eq('user_id', user.id)
        .eq('is_active', true)
        .single();

      if (targetError && targetError.code !== 'PGRST116') {
        console.error('Error loading savings target:', targetError);
      } else if (targetData) {
        setSavingsTarget(targetData);

        // Load recent deposits for this target
        const { data: depositsData, error: depositsError } = await supabase
          .from('savings_deposits')
          .select('*')
          .eq('savings_target_id', targetData.id)
          .order('deposit_date', { ascending: false })
          .limit(5);

        if (depositsError) {
          console.error('Error loading deposits:', depositsError);
        } else {
          setRecentDeposits(depositsData || []);
        }
      }
    } catch (error) {
      console.error('Error loading savings data:', error);
      Alert.alert('Error', 'Gagal memuat data tabungan');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    loadSavingsData();
  }, [user]);

  const onRefresh = () => {
    setRefreshing(true);
    loadSavingsData();
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('id-ID', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    });
  };

  const progressPercentage = savingsTarget 
    ? Math.min(Math.round((savingsTarget.current_amount / savingsTarget.target_amount) * 100), 100)
    : 0;

  const remainingAmount = savingsTarget 
    ? Math.max(savingsTarget.target_amount - savingsTarget.current_amount, 0)
    : 0;

  if (loading) {
    return (
      <SafeAreaView style={tabunganStyles.container}>
        <View style={tabunganStyles.loadingContainer}>
          <Text style={tabunganStyles.loadingText}>Memuat data tabungan...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={tabunganStyles.container}>
      <ScrollView 
        contentContainerStyle={tabunganStyles.scrollContent}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Header */}
        <View style={tabunganStyles.header}>
          <Text style={tabunganStyles.pageTitle}>💰 Menu Tabungan</Text>
          <Text style={tabunganStyles.subtitle}>Kelola tabungan Anda dengan mudah</Text>
        </View>

        {savingsTarget ? (
          <>
            {/* Progress Section */}
            <View style={tabunganStyles.progressSection}>
              <Text style={tabunganStyles.sectionTitle}>Target Tabungan Anda</Text>
              <Text style={tabunganStyles.targetName}>{savingsTarget.target_name}</Text>
              
              <View style={tabunganStyles.amountContainer}>
                <Text style={tabunganStyles.currentAmount}>
                  {formatCurrency(savingsTarget.current_amount)}
                </Text>
                <Text style={tabunganStyles.targetAmount}>
                  dari {formatCurrency(savingsTarget.target_amount)}
                </Text>
              </View>

              <View style={tabunganStyles.progressContainer}>
                <View style={tabunganStyles.progressBar}>
                  <View
                    style={[
                      tabunganStyles.progressFill,
                      { width: `${progressPercentage}%` }
                    ]}
                  />
                </View>
                <Text style={tabunganStyles.progressText}>{progressPercentage}%</Text>
              </View>

              <Text style={tabunganStyles.remainingText}>
                Sisa yang perlu ditabung: {formatCurrency(remainingAmount)}
              </Text>
            </View>

            {/* Menu Buttons */}
            <View style={tabunganStyles.menuSection}>
              <TouchableOpacity 
                style={tabunganStyles.menuButton}
                onPress={() => router.push('/tabungan/tambah-setoran')}
              >
                <View style={tabunganStyles.menuIcon}>
                  <Ionicons name="add-circle" size={32} color="#4CAF50" />
                </View>
                <View style={tabunganStyles.menuContent}>
                  <Text style={tabunganStyles.menuTitle}>Tambah Setoran Baru</Text>
                  <Text style={tabunganStyles.menuDescription}>
                    Tambahkan setoran ke tabungan Anda
                  </Text>
                </View>
                <Ionicons name="chevron-forward" size={24} color="#666" />
              </TouchableOpacity>

              <TouchableOpacity 
                style={tabunganStyles.menuButton}
                onPress={() => router.push('/tabungan/riwayat')}
              >
                <View style={tabunganStyles.menuIcon}>
                  <Ionicons name="list" size={32} color="#2196F3" />
                </View>
                <View style={tabunganStyles.menuContent}>
                  <Text style={tabunganStyles.menuTitle}>Riwayat Setoran</Text>
                  <Text style={tabunganStyles.menuDescription}>
                    Lihat semua setoran yang sudah dilakukan
                  </Text>
                </View>
                <Ionicons name="chevron-forward" size={24} color="#666" />
              </TouchableOpacity>

              <TouchableOpacity 
                style={tabunganStyles.menuButton}
                onPress={() => router.push('/tabungan/simulasi')}
              >
                <View style={tabunganStyles.menuIcon}>
                  <Ionicons name="calculator" size={32} color="#FF9800" />
                </View>
                <View style={tabunganStyles.menuContent}>
                  <Text style={tabunganStyles.menuTitle}>Simulasi Target</Text>
                  <Text style={tabunganStyles.menuDescription}>
                    Hitung berapa lagi yang perlu ditabung
                  </Text>
                </View>
                <Ionicons name="chevron-forward" size={24} color="#666" />
              </TouchableOpacity>
            </View>

            {/* Recent Deposits */}
            {recentDeposits.length > 0 && (
              <View style={tabunganStyles.recentSection}>
                <Text style={tabunganStyles.sectionTitle}>Setoran Terakhir</Text>
                {recentDeposits.slice(0, 3).map((deposit) => (
                  <View key={deposit.id} style={tabunganStyles.depositItem}>
                    <View style={tabunganStyles.depositIcon}>
                      <Ionicons name="arrow-up-circle" size={24} color="#4CAF50" />
                    </View>
                    <View style={tabunganStyles.depositInfo}>
                      <Text style={tabunganStyles.depositAmount}>
                        {formatCurrency(deposit.amount)}
                      </Text>
                      <Text style={tabunganStyles.depositDate}>
                        {formatDate(deposit.deposit_date)}
                      </Text>
                    </View>
                  </View>
                ))}
                <TouchableOpacity 
                  style={tabunganStyles.viewAllButton}
                  onPress={() => router.push('/tabungan/riwayat')}
                >
                  <Text style={tabunganStyles.viewAllText}>Lihat Semua Riwayat</Text>
                </TouchableOpacity>
              </View>
            )}
          </>
        ) : (
          /* No Savings Target */
          <View style={tabunganStyles.noTargetSection}>
            <Ionicons name="wallet-outline" size={64} color="#ccc" />
            <Text style={tabunganStyles.noTargetTitle}>Belum Ada Target Tabungan</Text>
            <Text style={tabunganStyles.noTargetDescription}>
              Mulai menabung dengan membuat target tabungan pertama Anda
            </Text>
            <TouchableOpacity 
              style={tabunganStyles.createTargetButton}
              onPress={() => router.push('/tabungan/buat-target')}
            >
              <Text style={tabunganStyles.createTargetText}>Buat Target Tabungan</Text>
            </TouchableOpacity>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}
